{"editor.formatOnPaste": false, "editor.formatOnSave": true, "editor.detectIndentation": false, "editor.defaultFormatter": "esbenp.prettier-vscode", "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[properties]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[dotenv]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "files.eol": "\n", "eslint.workingDirectories": [{"mode": "auto"}], "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "[dockerfile]": {"editor.defaultFormatter": "ms-azuretools.vscode-docker"}}