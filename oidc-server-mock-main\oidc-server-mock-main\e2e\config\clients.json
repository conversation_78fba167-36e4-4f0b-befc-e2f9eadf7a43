[{"ClientId": "implicit-flow-client-id", "Description": "Client for implicit flow", "AllowedGrantTypes": ["implicit"], "AllowAccessTokensViaBrowser": true, "RedirectUris": ["https://*.google.com"], "AllowedScopes": ["openid", "profile", "email", "some-custom-identity", "some-app-scope-1"], "IdentityTokenLifetime": 3600, "AccessTokenLifetime": 3600}, {"ClientId": "client-credentials-flow-client-id", "ClientSecrets": ["client-credentials-flow-client-secret"], "Description": "Client for client credentials flow", "AllowedGrantTypes": ["client_credentials"], "AllowedScopes": ["some-app-scope-1"], "ClientClaimsPrefix": "", "Claims": [{"Type": "string_claim", "Value": "string_claim_value", "ValueType": "string"}, {"Type": "json_claim", "Value": "[\"value1\", \"value2\"]", "ValueType": "json"}]}, {"ClientId": "password-flow-client-id", "ClientSecrets": ["password-flow-client-secret"], "Description": "Client for password flow", "AllowedGrantTypes": ["password"], "AllowedScopes": ["openid", "profile", "email", "some-custom-identity", "some-app-scope-1"], "ClientClaimsPrefix": "", "Claims": [{"Type": "string_claim", "Value": "string_claim_value", "ValueType": "string"}, {"Type": "json_claim", "Value": "[\"value1\", \"value2\"]", "ValueType": "json"}], "RequireClientSecret": false}, {"ClientId": "authorization-code-client-id", "ClientSecrets": ["authorization-code-client-secret"], "Description": "Client for authorization code flow", "AllowedGrantTypes": ["authorization_code"], "AllowAccessTokensViaBrowser": true, "RedirectUris": ["https://*.google.com"], "RequirePkce": false, "AllowedScopes": ["openid", "profile", "email", "some-custom-identity", "some-app-scope-1"], "IdentityTokenLifetime": 3600, "AccessTokenLifetime": 3600, "RequireClientSecret": false}, {"ClientId": "authorization-code-with-pkce-client-id", "ClientSecrets": ["authorization-code-with-pkce-client-secret"], "Description": "Client for authorization code flow", "AllowedGrantTypes": ["authorization_code"], "AllowAccessTokensViaBrowser": true, "RedirectUris": ["https://*.google.com"], "AllowedScopes": ["openid", "profile", "email", "some-custom-identity", "some-app-scope-1"], "IdentityTokenLifetime": 3600, "AccessTokenLifetime": 3600, "RequireClientSecret": false}]