version: '3'
services:
  oidc-server-mock:
    container_name: oidc-server-mock
    image: ghcr.io/soluto/oidc-server-mock:latest
    ports:
      - '4011:80'
    environment:
      ASPNETCORE_ENVIRONMENT: Development
      SERVER_OPTIONS_INLINE: |
        {
          "AccessTokenJwtType": "JWT",
          "Discovery": {
            "ShowKeySet": true
          },
          "Authentication": {
            "CookieSameSiteMode": "Lax",
            "CheckSessionCookieSameSiteMode": "Lax"
          }
        }
      LOGIN_OPTIONS_INLINE: |
        {
          "AllowRememberLogin": false
        }
      LOGOUT_OPTIONS_INLINE: |
        {
          "AutomaticRedirectAfterSignOut": true
        }
      API_SCOPES_INLINE: |
        - Name: some-app-scope-1
        - Name: some-app-scope-2
      API_RESOURCES_INLINE: |
        - Name: some-app
          Scopes:
            - some-app-scope-1
            - some-app-scope-2
      USERS_CONFIGURATION_INLINE: |
        [
          {
            "SubjectId":"1",
            "Username":"User1",
            "Password":"pwd",
            "Claims": [
              {
                "Type": "name",
                "Value": "Sam Tailor",
                "ValueType": "string"
              },
              {
                "Type": "email",
                "Value": "<EMAIL>",
                "ValueType": "string"
              },
              {
                "Type": "some-api-resource-claim",
                "Value": "Sam's Api Resource Custom Claim",
                "ValueType": "string"
              },
              {
                "Type": "some-api-scope-claim",
                "Value": "Sam's Api Scope Custom Claim",
                "ValueType": "string"
              },
              {
                "Type": "some-identity-resource-claim",
                "Value": "Sam's Identity Resource Custom Claim",
                "ValueType": "string"
              }
            ]
          }
        ]
      CLIENTS_CONFIGURATION_PATH: /tmp/config/clients-config.json
      ASPNET_SERVICES_OPTIONS_INLINE: |
        {
          "ForwardedHeadersOptions": {
            "ForwardedHeaders" : "All"
          }
        }
    volumes:
      - .:/tmp/config:ro