// Jest <PERSON> v1, https://goo.gl/fbAQLP

exports[`Implicit Flow - simple_user - Authorization Endpoint (id_token only) 1`] = `
{
  "alg": "RS256",
  "typ": "JWT",
  "iss": "https://localhost:8443",
  "aud": "implicit-flow-client-id",
  "amr": [
    "pwd"
  ],
  "nonce": "xyz",
  "sub": "simple_user",
  "idp": "local"
}
`;

exports[`Implicit Flow - simple_user - Authorization Endpoint 1`] = `
{
  "alg": "RS256",
  "typ": "JWT",
  "iss": "https://localhost:8443",
  "aud": "some-app",
  "scope": [
    "openid",
    "profile",
    "email",
    "some-custom-identity",
    "some-app-scope-1"
  ],
  "amr": [
    "pwd"
  ],
  "client_id": "implicit-flow-client-id",
  "sub": "simple_user",
  "idp": "local"
}
`;

exports[`Implicit Flow - simple_user - Introspection Endpoint 1`] = `
{
  "iss": "https://localhost:8443",
  "aud": "some-app",
  "amr": "pwd",
  "client_id": "implicit-flow-client-id",
  "sub": "simple_user",
  "idp": "local",
  "active": true,
  "scope": "some-app-scope-1"
}
`;

exports[`Implicit Flow - simple_user - UserInfo Endpoint 1`] = `
{
  "sub": "simple_user",
}
`;

exports[`Implicit Flow - user_with_all_claim_types - Authorization Endpoint (id_token only) 1`] = `
{
  "alg": "RS256",
  "typ": "JWT",
  "iss": "https://localhost:8443",
  "aud": "implicit-flow-client-id",
  "amr": [
    "pwd"
  ],
  "nonce": "xyz",
  "sub": "user_with_all_claim_types",
  "idp": "local",
  "name": "Oliver Hunter",
  "email": "<EMAIL>",
  "some-custom-identity-user-claim": "Oliver's Custom User Claim"
}
`;

exports[`Implicit Flow - user_with_all_claim_types - Authorization Endpoint 1`] = `
{
  "alg": "RS256",
  "typ": "JWT",
  "iss": "https://localhost:8443",
  "aud": "some-app",
  "scope": [
    "openid",
    "profile",
    "email",
    "some-custom-identity",
    "some-app-scope-1"
  ],
  "amr": [
    "pwd"
  ],
  "client_id": "implicit-flow-client-id",
  "sub": "user_with_all_claim_types",
  "idp": "local",
  "some-app-user-custom-claim": "Oliver's Custom User Claim",
  "some-app-scope-1-custom-user-claim": "Oliver's Scope Custom User Claim"
}
`;

exports[`Implicit Flow - user_with_all_claim_types - Introspection Endpoint 1`] = `
{
  "iss": "https://localhost:8443",
  "aud": "some-app",
  "amr": "pwd",
  "client_id": "implicit-flow-client-id",
  "sub": "user_with_all_claim_types",
  "idp": "local",
  "some-app-user-custom-claim": "Oliver's Custom User Claim",
  "some-app-scope-1-custom-user-claim": "Oliver's Scope Custom User Claim",
  "active": true,
  "scope": "some-app-scope-1"
}
`;

exports[`Implicit Flow - user_with_all_claim_types - UserInfo Endpoint 1`] = `
{
  "email": "<EMAIL>",
  "name": "Oliver Hunter",
  "some-custom-identity-user-claim": "Oliver's Custom User Claim",
  "sub": "user_with_all_claim_types",
}
`;

exports[`Implicit Flow - user_with_custom_api_resource_claims - Authorization Endpoint (id_token only) 1`] = `
{
  "alg": "RS256",
  "typ": "JWT",
  "iss": "https://localhost:8443",
  "aud": "implicit-flow-client-id",
  "amr": [
    "pwd"
  ],
  "nonce": "xyz",
  "sub": "user_with_custom_api_resource_claims",
  "idp": "local",
  "name": "Sam Tailor",
  "email": "<EMAIL>"
}
`;

exports[`Implicit Flow - user_with_custom_api_resource_claims - Authorization Endpoint 1`] = `
{
  "alg": "RS256",
  "typ": "JWT",
  "iss": "https://localhost:8443",
  "aud": "some-app",
  "scope": [
    "openid",
    "profile",
    "email",
    "some-custom-identity",
    "some-app-scope-1"
  ],
  "amr": [
    "pwd"
  ],
  "client_id": "implicit-flow-client-id",
  "sub": "user_with_custom_api_resource_claims",
  "idp": "local",
  "some-app-user-custom-claim": "Sam's Custom User Claim",
  "some-app-scope-1-custom-user-claim": "Sam's Scope Custom User Claim"
}
`;

exports[`Implicit Flow - user_with_custom_api_resource_claims - Introspection Endpoint 1`] = `
{
  "iss": "https://localhost:8443",
  "aud": "some-app",
  "amr": "pwd",
  "client_id": "implicit-flow-client-id",
  "sub": "user_with_custom_api_resource_claims",
  "idp": "local",
  "some-app-user-custom-claim": "Sam's Custom User Claim",
  "some-app-scope-1-custom-user-claim": "Sam's Scope Custom User Claim",
  "active": true,
  "scope": "some-app-scope-1"
}
`;

exports[`Implicit Flow - user_with_custom_api_resource_claims - UserInfo Endpoint 1`] = `
{
  "email": "<EMAIL>",
  "name": "Sam Tailor",
  "sub": "user_with_custom_api_resource_claims",
}
`;

exports[`Implicit Flow - user_with_custom_identity_claims - Authorization Endpoint (id_token only) 1`] = `
{
  "alg": "RS256",
  "typ": "JWT",
  "iss": "https://localhost:8443",
  "aud": "implicit-flow-client-id",
  "amr": [
    "pwd"
  ],
  "nonce": "xyz",
  "sub": "user_with_custom_identity_claims",
  "idp": "local",
  "name": "Jack Sparrow",
  "email": "<EMAIL>",
  "some-custom-identity-user-claim": "Jack's Custom User Claim"
}
`;

exports[`Implicit Flow - user_with_custom_identity_claims - Authorization Endpoint 1`] = `
{
  "alg": "RS256",
  "typ": "JWT",
  "iss": "https://localhost:8443",
  "aud": "some-app",
  "scope": [
    "openid",
    "profile",
    "email",
    "some-custom-identity",
    "some-app-scope-1"
  ],
  "amr": [
    "pwd"
  ],
  "client_id": "implicit-flow-client-id",
  "sub": "user_with_custom_identity_claims",
  "idp": "local"
}
`;

exports[`Implicit Flow - user_with_custom_identity_claims - Introspection Endpoint 1`] = `
{
  "iss": "https://localhost:8443",
  "aud": "some-app",
  "amr": "pwd",
  "client_id": "implicit-flow-client-id",
  "sub": "user_with_custom_identity_claims",
  "idp": "local",
  "active": true,
  "scope": "some-app-scope-1"
}
`;

exports[`Implicit Flow - user_with_custom_identity_claims - UserInfo Endpoint 1`] = `
{
  "email": "<EMAIL>",
  "name": "Jack Sparrow",
  "some-custom-identity-user-claim": "Jack's Custom User Claim",
  "sub": "user_with_custom_identity_claims",
}
`;

exports[`Implicit Flow - user_with_standard_claims - Authorization Endpoint (id_token only) 1`] = `
{
  "alg": "RS256",
  "typ": "JWT",
  "iss": "https://localhost:8443",
  "aud": "implicit-flow-client-id",
  "amr": [
    "pwd"
  ],
  "nonce": "xyz",
  "sub": "user_with_standard_claims",
  "idp": "local",
  "name": "John Smith",
  "email": "<EMAIL>",
  "email_verified": "true"
}
`;

exports[`Implicit Flow - user_with_standard_claims - Authorization Endpoint 1`] = `
{
  "alg": "RS256",
  "typ": "JWT",
  "iss": "https://localhost:8443",
  "aud": "some-app",
  "scope": [
    "openid",
    "profile",
    "email",
    "some-custom-identity",
    "some-app-scope-1"
  ],
  "amr": [
    "pwd"
  ],
  "client_id": "implicit-flow-client-id",
  "sub": "user_with_standard_claims",
  "idp": "local"
}
`;

exports[`Implicit Flow - user_with_standard_claims - Introspection Endpoint 1`] = `
{
  "iss": "https://localhost:8443",
  "aud": "some-app",
  "amr": "pwd",
  "client_id": "implicit-flow-client-id",
  "sub": "user_with_standard_claims",
  "idp": "local",
  "active": true,
  "scope": "some-app-scope-1"
}
`;

exports[`Implicit Flow - user_with_standard_claims - UserInfo Endpoint 1`] = `
{
  "email": "<EMAIL>",
  "email_verified": "true",
  "name": "John Smith",
  "sub": "user_with_standard_claims",
}
`;
