name: Build and push new version

on:
  create:

env:
  TILT_VERSION: 'v0.33.21'

jobs:
  build_push_docker:
    name: Build and Push Docker image
    if: ${{ startsWith(github.ref, 'refs/tags/v') }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Login to GHCR
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - id: get_version
        name: Format docker image tag
        uses: battila7/get-version-action@v2

      - id: repository_owner
        name: Format repository owner
        uses: ASzc/change-string-case-action@v6
        with:
          string: ${{ github.repository_owner }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and export to Docker
        uses: docker/build-push-action@v6
        with:
          load: true
          context: ./src
          file: ./src/Dockerfile
          tags: ghcr.io/${{ steps.repository_owner.outputs.lowercase }}/oidc-server-mock:${{ steps.get_version.outputs.version-without-v }}-test

      - name: Setup Tilt
        run: curl -fsSL https://raw.githubusercontent.com/tilt-dev/tilt/${TILT_VERSION}/scripts/install.sh | bash

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: pnpm

      - name: Run npm install
        run: pnpm install --frozen-lockfile

      - name: Run Tests
        run: pnpm run tilt:ci
        env:
          IMAGE_TAG: ${{ steps.get_version.outputs.version-without-v }}-test

      - name: Build and push new docker image
        uses: docker/build-push-action@v6
        with:
          push: true
          context: ./src
          file: ./src/Dockerfile
          platforms: linux/amd64,linux/arm64
          tags: |
            ghcr.io/${{ steps.repository_owner.outputs.lowercase }}/oidc-server-mock:latest
            ghcr.io/${{ steps.repository_owner.outputs.lowercase }}/oidc-server-mock:${{ steps.get_version.outputs.version-without-v }}
          labels: |
            org.opencontainers.image.source=${{ github.event.repository.html_url }}

  build_push_nuget:
    name: Build and Push Nuget package
    if: ${{ startsWith(github.ref, 'refs/tags/v') }}
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    defaults:
      run:
        working-directory: src
    env:
      NUGET_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      dotnet-version: '8.0'
    steps:
      - uses: actions/checkout@v4

      - name: Download UI
        run: ./getui.sh

      - name: Setup .NET Core SDK ${{ env.dotnet-version }}
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.dotnet-version }}
          source-url: https://nuget.pkg.github.com/${{ github.repository_owner }}/index.json

      - name: Install dependencies
        run: dotnet restore

      - id: get_version
        name: Format nuget package version
        uses: battila7/get-version-action@v2

      - name: Build Nuget package
        run: |
          dotnet pack --no-restore --configuration Release \
            /p:VersionPrefix=${{ steps.get_version.outputs.version-without-v }} \
            /p:RepositoryCommit=${{ github.sha }}

      - name: Push Nuget package
        run: dotnet nuget push bin/Release/*.nupkg -k ${{ env.NUGET_AUTH_TOKEN }}
