# Use the base image from Soluto OIDC mock server
FROM ghcr.io/soluto/oidc-server-mock:0.8.6
 
# Set environment variables for configuration
ENV ASPNETCORE_ENVIRONMENT=Development
 
# Configure the server options
ENV SERVER_OPTIONS_INLINE="{ \
  \"AccessTokenJwtType\": \"JWT\", \
  \"Discovery\": { \
    \"ShowKeySet\": true \
  }, \
  \"Authentication\": { \
    \"CookieSameSiteMode\": \"Lax\", \
    \"CheckSessionCookieSameSiteMode\": \"Lax\" \
  } \
}"
 
# Set up user configuration inline (ensure proper escaping for the JSON structure)
ENV USERS_CONFIGURATION_INLINE="[ \
  { \
    \"SubjectId\": \"1\", \
    \"Username\": \"User1\", \
    \"Password\": \"pwd\", \
    \"Claims\": [ \
      { \"Type\": \"name\", \"Value\": \"<PERSON> Tailor\" }, \
      { \"Type\": \"email\", \"Value\": \"<EMAIL>\" }, \
      { \"Type\": \"some-api-resource-claim\", \"Value\": \"Sam's Api Resource Custom Claim\" }, \
      { \"Type\": \"some-api-scope-claim\", \"Value\": \"Sam's Api Scope Custom Claim\" }, \
      { \"Type\": \"some-identity-resource-claim\", \"Value\": \"Sam's Identity Resource Custom Claim\" } \
    ] \
  }, \
  { \
    \"SubjectId\": \"2\", \
    \"Username\": \"Mark\", \
    \"Password\": \"pwd\", \
    \"Claims\": [ \
      { \"Type\": \"name\", \"Value\": \"Mark Ning\" }, \
      { \"Type\": \"email\", \"Value\": \"<EMAIL>\" }, \
      { \"Type\": \"some-api-resource-claim\", \"Value\": \"Mark's Api Resource Custom Claim\" }, \
      { \"Type\": \"some-api-scope-claim\", \"Value\": \"Mark's Api Scope Custom Claim\" }, \
      { \"Type\": \"some-identity-resource-claim\", \"Value\": \"Mark's Identity Resource Custom Claim\" } \
    ] \
  }, \
  { \
    \"SubjectId\": \"3\", \
    \"Username\": \"EntityAnalyst\", \
    \"Password\": \"pwd\", \
    \"Claims\": [ \
      { \"Type\": \"name\", \"Value\": \"Entity Analyst\" }, \
      { \"Type\": \"email\", \"Value\": \"<EMAIL>\" } \
    ] \
  }, \
  { \
    \"SubjectId\": \"4\", \
    \"Username\": \"EntityManager\", \
    \"Password\": \"pwd\", \
    \"Claims\": [ \
      { \"Type\": \"name\", \"Value\": \"Entity Manager\" }, \
      { \"Type\": \"email\", \"Value\": \"<EMAIL>\" } \
    ] \
  }, \
  { \
    \"SubjectId\": \"5\", \
    \"Username\": \"EntitySeniorApprover\", \
    \"Password\": \"pwd\", \
    \"Claims\": [ \
      { \"Type\": \"name\", \"Value\": \"Entity Senior Approver\" }, \
      { \"Type\": \"email\", \"Value\": \"<EMAIL>\" } \
    ] \
  }, \
  { \
    \"SubjectId\": \"6\", \
    \"Username\": \"PlatformAnalyst\", \
    \"Password\": \"pwd\", \
    \"Claims\": [ \
      { \"Type\": \"name\", \"Value\": \"Platform Analyst\" }, \
      { \"Type\": \"email\", \"Value\": \"<EMAIL>\" } \
    ] \
  }, \
  { \
    \"SubjectId\": \"7\", \
    \"Username\": \"PlatformAdmin\", \
    \"Password\": \"pwd\", \
    \"Claims\": [ \
      { \"Type\": \"name\", \"Value\": \"Platform Admin\" }, \
      { \"Type\": \"email\", \"Value\": \"<EMAIL>\" } \
    ] \
  } \
]"
 
# Client configuration inline
ENV CLIENTS_CONFIGURATION_INLINE="[ \
  { \
    \"ClientId\": \"implicit-mock-client\", \
    \"RequireClientSecret\": false, \
    \"Description\": \"Client for authorization code flow\", \
    \"AllowedGrantTypes\": [\"authorization_code\"], \
    \"RequirePkce\": true, \
    \"AllowAccessTokensViaBrowser\": true, \
    \"RedirectUris\": [ \
      \"http://localhost:3000/auth/oidc\", \
      \"http://localhost:4004/auth/oidc\", \
      \"https://localhost:7265/fake-uae-pass/callback\", \
      \"https://localhost:44343/callback\", \
      \"http://localhost:8000/callback\", \
      \"https://localhost:7230/callback\" \
    ], \
    \"AllowedScopes\": [\"openid\", \"profile\", \"email\"], \
    \"IdentityTokenLifetime\": 3600, \
    \"AccessTokenLifetime\": 3600 \
  }, \
  { \
    \"ClientId\": \"client-credentials-mock-client\", \
    \"ClientSecrets\": [\"client-credentials-mock-client-secret\"], \
    \"Description\": \"Client for client credentials flow\", \
    \"AllowedGrantTypes\": [\"client_credentials\"], \
    \"AllowedScopes\": [\"some-app-scope-1\"], \
    \"ClientClaimsPrefix\": \"\", \
    \"Claims\": [ \
      { \"Type\": \"string_claim\", \"Value\": \"string_claim_value\" }, \
      { \"Type\": \"json_claim\", \"Value\": \"[\\\"value1\\\", \\\"value2\\\"]\" } \
    ] \
  } \
]"
 
# API scopes configuration
ENV API_SCOPES_INLINE="[ \
  { \"Name\": \"some-app-scope-1\" }, \
  { \"Name\": \"some-app-scope-2\" } \
]"
 
# Set ASP.NET forwarded headers options
ENV ASPNET_SERVICES_OPTIONS_INLINE="{ \
  \"ForwardedHeadersOptions\": { \
    \"ForwardedHeaders\": \"All\" \
  } \
}"
 
# Expose the default port 80 for the mock server
EXPOSE 80
 
# Run the OIDC server mock application
CMD ["dotnet", "Soluto.OidcServerMock.dll"]