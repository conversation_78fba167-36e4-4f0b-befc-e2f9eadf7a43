<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>

    <IsPackable>true</IsPackable>
    <Description>Configurable mock server with OpenId Connect functionality</Description>
    <VersionPrefix>0.10.1</VersionPrefix>
    <PackageProjectUrl>https://github.com/Soluto/oidc-server-mock</PackageProjectUrl>
    <PackageLicenseExpression>Apache-2.0</PackageLicenseExpression>
    <PackageTags>OIDC</PackageTags>
    <RepositoryUrl>https://github.com/Soluto/oidc-server-mock</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackAsTool>true</PackAsTool>
    <ToolCommandName>oidc-mock</ToolCommandName>
    <IncludeContentInPack>false</IncludeContentInPack>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="keys\**" />
    <Content Remove="wwwroot\**" />
    <EmbeddedResource Include="wwwroot\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Duende.IdentityServer" Version="7.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.11" />
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="8.0.11" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="YamlDotNet" Version="15.3.0" />
  </ItemGroup>

</Project>
