// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`User management Introspection Endpoint 1`] = `
{
  "iss": "https://localhost:8443",
  "aud": "some-app",
  "amr": "pwd",
  "client_id": "password-flow-client-id",
  "sub": {
    "inverse": false
  },
  "idp": "local",
  "some-app-user-custom-claim": {
    "inverse": false
  },
  "some-app-scope-1-custom-user-claim": {
    "inverse": false
  },
  "active": true,
  "scope": "some-app-scope-1"
}
`;

exports[`User management Token Endpoint 1`] = `
{
  "alg": "RS256",
  "typ": "JWT",
  "iss": "https://localhost:8443",
  "aud": "some-app",
  "scope": [
    "email",
    "openid",
    "profile",
    "some-app-scope-1",
    "some-custom-identity"
  ],
  "amr": [
    "pwd"
  ],
  "client_id": "password-flow-client-id",
  "sub": {
    "inverse": false
  },
  "idp": "local",
  "some-app-user-custom-claim": {
    "inverse": false
  },
  "some-app-scope-1-custom-user-claim": {
    "inverse": false
  }
}
`;

exports[`User management UserInfo Endpoint 1`] = `
{
  "email": Any<String>,
  "name": Any<String>,
  "some-custom-identity-user-claim": Any<String>,
  "sub": Any<String>,
}
`;
