//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("OpenIdConnectServerMock")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("Configurable mock server with OpenId Connect functionality")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("********")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("0.10.1")]
[assembly: System.Reflection.AssemblyProductAttribute("OpenIdConnectServerMock")]
[assembly: System.Reflection.AssemblyTitleAttribute("OpenIdConnectServerMock")]
[assembly: System.Reflection.AssemblyVersionAttribute("********")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://github.com/Soluto/oidc-server-mock")]

// Generated by the MSBuild WriteCodeFragment class.

