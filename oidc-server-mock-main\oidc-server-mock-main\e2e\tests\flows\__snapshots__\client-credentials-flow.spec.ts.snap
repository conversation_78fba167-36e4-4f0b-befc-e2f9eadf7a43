// Jest <PERSON> v1, https://goo.gl/fbAQLP

exports[`Client Credentials Flow Introspection Endpoint 1`] = `
{
  "iss": "https://localhost:8443",
  "aud": "some-app",
  "client_id": "client-credentials-flow-client-id",
  "string_claim": "string_claim_value",
  "json_claim": [
    "value1",
    "value2"
  ],
  "active": true,
  "scope": "some-app-scope-1"
}
`;

exports[`Client Credentials Flow Token Endpoint 1`] = `
{
  "alg": "RS256",
  "typ": "JWT",
  "iss": "https://localhost:8443",
  "aud": "some-app",
  "scope": [
    "some-app-scope-1"
  ],
  "client_id": "client-credentials-flow-client-id",
  "string_claim": "string_claim_value",
  "json_claim": [
    "value1",
    "value2"
  ]
}
`;
