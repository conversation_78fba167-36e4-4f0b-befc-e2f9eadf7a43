{"name": "e2e", "version": "1.0.0", "scripts": {"test": "NODE_TLS_REJECT_UNAUTHORIZED=0 jest --runInBand --ci --config jest.config.ts"}, "license": "MIT", "engines": {"node": ">=20.0.0"}, "dependencies": {"chance": "^1.1.12", "dotenv": "^16.4.7", "jws": "^4.0.0", "playwright-chromium": "^1.49.1", "wait-on": "^8.0.1", "yaml": "^2.6.1"}, "devDependencies": {"@jest/types": "^29.6.3", "@types/chance": "^1.1.6", "@types/jest": "^29.5.14", "@types/jws": "^3.2.10", "@types/node": "^22.10.1", "@types/wait-on": "^5.3.4", "jest": "^29.7.0", "jest-playwright-preset": "^4.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.2"}}