{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive - Whiteshield\\Desktop\\DGHR\\PHASE 3\\oidc-server-mock-main (2)\\oidc-server-mock-main\\oidc-server-mock-main\\src\\OpenIdConnectServerMock.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive - Whiteshield\\Desktop\\DGHR\\PHASE 3\\oidc-server-mock-main (2)\\oidc-server-mock-main\\oidc-server-mock-main\\src\\OpenIdConnectServerMock.csproj": {"version": "0.10.1", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive - Whiteshield\\Desktop\\DGHR\\PHASE 3\\oidc-server-mock-main (2)\\oidc-server-mock-main\\oidc-server-mock-main\\src\\OpenIdConnectServerMock.csproj", "projectName": "OpenIdConnectServerMock", "projectPath": "C:\\Users\\<USER>\\OneDrive - Whiteshield\\Desktop\\DGHR\\PHASE 3\\oidc-server-mock-main (2)\\oidc-server-mock-main\\oidc-server-mock-main\\src\\OpenIdConnectServerMock.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive - Whiteshield\\Desktop\\DGHR\\PHASE 3\\oidc-server-mock-main (2)\\oidc-server-mock-main\\oidc-server-mock-main\\src\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Duende.IdentityServer": {"target": "Package", "version": "[7.0.8, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[8.0.11, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.3, )"}, "YamlDotNet": {"target": "Package", "version": "[15.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}